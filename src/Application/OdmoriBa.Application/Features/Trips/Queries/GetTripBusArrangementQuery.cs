using OdmoriBa.Application.Features.Trips.Models;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetTripBusArrangementQuery(Guid TripId) : IQuery<Result<TripBusArrangementDto>>;

internal sealed class GetTripBusArrangementQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetTripBusArrangementQuery, Result<TripBusArrangementDto>>
{
    public async ValueTask<Result<TripBusArrangementDto>> Handle(GetTripBusArrangementQuery request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(t => t.TripBuses)
                .ThenInclude(tb => tb.Bus)
            .Include(t => t.TravelParties)
                .ThenInclude(tp => tp.Travelers)
                    .ThenInclude(tr => tr.Person)
            .Include(t => t.TravelParties)
                .ThenInclude(tp => tp.MainContact)
            .FirstOrDefaultAsync(t => t.Id == request.TripId, cancellationToken);

        if (trip == null)
        {
            return TripErrors.TripNotFound(request.TripId);
        }

        var departureBuses = trip.TripBuses
            .Where(tb => tb.Direction == TripBusDirection.Departure)
            .Select(tb => new TripBusDto
            {
                Id = tb.Id,
                Name = tb.Name,
                BusId = tb.BusId,
                BusName = tb.Bus?.Name ?? "",
                Capacity = tb.Bus?.Capacity ?? 0,
                Direction = tb.Direction,
                AssignedTravelers = trip.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Where(tr => tr.DepartureTripBusId == tb.Id)
                    .Select(tr => new TravelerSeatAssignmentDto
                    {
                        Id = tr.Id,
                        PersonName = $"{tr.Person.FirstName} {tr.Person.LastName}",
                        SeatNumber = tr.DepartureSeatNumber,
                        TravelPartyId = tr.TravelPartyId
                    })
                    .ToList()
            })
            .ToList();

        var returnBuses = trip.TripBuses
            .Where(tb => tb.Direction == TripBusDirection.Return)
            .Select(tb => new TripBusDto
            {
                Id = tb.Id,
                Name = tb.Name,
                BusId = tb.BusId,
                BusName = tb.Bus?.Name ?? "",
                Capacity = tb.Bus?.Capacity ?? 0,
                Direction = tb.Direction,
                AssignedTravelers = trip.TravelParties
                    .SelectMany(tp => tp.Travelers)
                    .Where(tr => tr.ReturnTripBusId == tb.Id)
                    .Select(tr => new TravelerSeatAssignmentDto
                    {
                        Id = tr.Id,
                        PersonName = $"{tr.Person.FirstName} {tr.Person.LastName}",
                        SeatNumber = tr.ReturnSeatNumber,
                        TravelPartyId = tr.TravelPartyId
                    })
                    .ToList()
            })
            .ToList();

        var travelParties = trip.TravelParties
            .Select(tp => new TravelPartyBusArrangementDto
            {
                Id = tp.Id,
                MainContactName = $"{tp.MainContact?.FirstName} {tp.MainContact?.LastName}",
                Travelers = tp.Travelers.Select(tr => new TravelerBusArrangementDto
                {
                    Id = tr.Id,
                    PersonName = $"{tr.Person.FirstName} {tr.Person.LastName}",
                    DepartureTripBusId = tr.DepartureTripBusId,
                    DepartureSeatNumber = tr.DepartureSeatNumber,
                    ReturnTripBusId = tr.ReturnTripBusId,
                    ReturnSeatNumber = tr.ReturnSeatNumber,
                    TravelPartyId = tr.TravelPartyId
                }).ToList()
            })
            .ToList();

        return new TripBusArrangementDto
        {
            TripId = trip.Id,
            TripName = trip.Name,
            DepartureBuses = departureBuses,
            ReturnBuses = returnBuses,
            TravelParties = travelParties
        };
    }
}
