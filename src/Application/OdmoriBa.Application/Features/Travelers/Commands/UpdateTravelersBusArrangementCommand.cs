using OdmoriBa.Application.Features.Travelers.Models;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record UpdateTravelersBusArrangementCommand(
    Guid TripId,
    List<TravelerBusAssignmentDto> TravelerAssignments) : ICommand<Result>;

public sealed record TravelerBusAssignmentDto(
    Guid TravelerId,
    Guid? DepartureTripBusId,
    int? DepartureSeatNumber,
    Guid? ReturnTripBusId,
    int? ReturnSeatNumber);

internal sealed class UpdateTravelersBusArrangementCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelersBusArrangementCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTravelersBusArrangementCommand request, CancellationToken cancellationToken)
    {
        // Load all travel parties for the trip with their travelers
        var travelParties = await dbContext.TravelParties
            .Where(tp => tp.TripId == request.TripId)
            .Include(tp => tp.Travelers)
            .ToListAsync(cancellationToken);

        if (!travelParties.Any())
        {
            return TripErrors.TripNotFound(request.TripId);
        }

        // Process each traveler assignment
        foreach (var assignment in request.TravelerAssignments)
        {
            // Find the travel party that contains this traveler
            var travelParty = travelParties.FirstOrDefault(tp => 
                tp.Travelers.Any(t => t.Id == assignment.TravelerId));

            if (travelParty == null)
            {
                return TripErrors.TravelerNotFound(assignment.TravelerId);
            }

            // Update the bus and seat assignment for this traveler
            var result = travelParty.UpdateBusAndSeat(
                assignment.TravelerId,
                assignment.DepartureTripBusId,
                assignment.DepartureSeatNumber,
                assignment.ReturnTripBusId,
                assignment.ReturnSeatNumber);

            if (result.IsError)
            {
                return result.Error;
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);
        return Result.Ok();
    }
}
