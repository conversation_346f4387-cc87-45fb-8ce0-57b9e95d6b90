@page "/trips/{TripId:guid}/bus-arrangement"
@using OdmoriBa.Application.Features.Trips.Queries
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Application.Features.Travelers.Commands
@using OdmoriBa.Core.Domains.Trips.Entities
@inject IMediator Mediator
@inject ISnackbar Snackbar

<PageTitle>Bus Arrangement - @(_tripData?.TripName ?? "Loading...")</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
    @if (_loading)
    {
        <MudProgressCircular Indeterminate="true" />
        <MudText>Loading trip data...</MudText>
    }
    else if (_tripData != null)
    {
        <MudText Typo="Typo.h4" Class="mb-4">Bus Arrangement - @_tripData.TripName</MudText>
        
        <MudGrid>
            <!-- Travel Parties List -->
            <MudItem xs="12" md="3">
                <MudPaper Class="pa-4" Style="height: 80vh; overflow-y: auto;">
                    <MudText Typo="Typo.h6" Class="mb-3">Travel Parties</MudText>
                    @foreach (var travelParty in _tripData.TravelParties)
                    {
                        <MudExpansionPanels Class="mb-2">
                            <MudExpansionPanel Text="@travelParty.MainContactName">
                                @foreach (var traveler in travelParty.Travelers)
                                {
                                    <div class="traveler-card pa-2 mb-2" 
                                         draggable="true" 
                                         @ondragstart="@(() => OnDragStart(traveler))"
                                         style="background: #f5f5f5; border-radius: 4px; cursor: move; border-left: 4px solid @GetTravelPartyColor(travelParty.Id);">
                                        <MudText Typo="Typo.body2">@traveler.PersonName</MudText>
                                        <MudText Typo="Typo.caption" Class="text-muted">
                                            Dep: @(GetBusName(traveler.DepartureTripBusId, TripBusDirection.Departure)) 
                                            @(traveler.DepartureSeatNumber?.ToString() ?? "No seat")
                                        </MudText>
                                        <MudText Typo="Typo.caption" Class="text-muted">
                                            Ret: @(GetBusName(traveler.ReturnTripBusId, TripBusDirection.Return)) 
                                            @(traveler.ReturnSeatNumber?.ToString() ?? "No seat")
                                        </MudText>
                                    </div>
                                }
                            </MudExpansionPanel>
                        </MudExpansionPanels>
                    }
                </MudPaper>
            </MudItem>

            <!-- Bus Arrangement Area -->
            <MudItem xs="12" md="9">
                <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                    <!-- Departure Buses Tab -->
                    <MudTabPanel Text="Departure Buses">
                        <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                            @foreach (var bus in _tripData.DepartureBuses)
                            {
                                <MudTabPanel Text="@bus.Name">
                                    <BusSeatGrid Bus="@bus" 
                                                OnSeatDrop="@((seatNumber) => OnSeatDrop(bus.Id, seatNumber, TripBusDirection.Departure))"
                                                OnTravelerRemove="@((travelerId) => OnTravelerRemove(travelerId, TripBusDirection.Departure))" />
                                </MudTabPanel>
                            }
                        </MudTabs>
                    </MudTabPanel>

                    <!-- Return Buses Tab -->
                    <MudTabPanel Text="Return Buses">
                        <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                            @foreach (var bus in _tripData.ReturnBuses)
                            {
                                <MudTabPanel Text="@bus.Name">
                                    <BusSeatGrid Bus="@bus" 
                                                OnSeatDrop="@((seatNumber) => OnSeatDrop(bus.Id, seatNumber, TripBusDirection.Return))"
                                                OnTravelerRemove="@((travelerId) => OnTravelerRemove(travelerId, TripBusDirection.Return))" />
                                </MudTabPanel>
                            }
                        </MudTabs>
                    </MudTabPanel>
                </MudTabs>
            </MudItem>
        </MudGrid>

        <!-- Save Button -->
        <MudFab Color="Color.Primary" 
               StartIcon="Icons.Material.Filled.Save" 
               Style="position: fixed; bottom: 20px; right: 20px;"
               OnClick="SaveArrangement"
               Disabled="_saving">
            @if (_saving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
            }
            else
            {
                <span>Save</span>
            }
        </MudFab>
    }
    else
    {
        <MudAlert Severity="Severity.Error">Failed to load trip data.</MudAlert>
    }
</MudContainer>
